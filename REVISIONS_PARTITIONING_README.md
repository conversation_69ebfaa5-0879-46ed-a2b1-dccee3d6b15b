# Particionamiento de Tabla Revisions

Este documento explica cómo implementar el particionamiento de la tabla `revisions` para mejorar el rendimiento con grandes volúmenes de datos.

## 🎯 Objetivo

Mejorar el rendimiento de consultas en la tabla `revisions` que contiene 2+ millones de registros, particionando por `revisionable_type` para que MySQL solo busque en las particiones relevantes.

## 📊 Distribución Actual de Datos

Basado en el análisis actual:
- `App\Models\BuildingsRelationships\BuildingContact`: ~2,829 registros (35.2%)
- `App\Models\User\Contact`: ~2,273 registros (28.3%)
- `App\Models\Building`: ~1,990 registros (24.7%)
- `App\User`: ~503 registros (6.3%)
- Otros modelos: ~5.5%

## 🚀 Instalación Rápida

### Opción 1: Script Automático (Recomendado)
```bash
./scripts/setup_revisions_partitioning.sh
```

### Opción 2: Paso a Paso Manual

#### 1. Crear tabla particionada
```bash
php artisan migrate --path=database/migrations/2024_12_20_000001_create_partitioned_revisions_table.php
```

#### 2. Migrar datos (dry run primero)
```bash
# Dry run para verificar
php artisan revisions:migrate-to-partitioned --dry-run

# Migración real
php artisan revisions:migrate-to-partitioned --batch-size=1000
```

#### 3. Verificar migración
```bash
php artisan revisions:analyze-partitions
```

#### 4. Hacer switch a tabla particionada
```bash
php artisan migrate --path=database/migrations/2024_12_20_000002_switch_to_partitioned_revisions.php
```

## 🔧 Comandos Disponibles

### `revisions:migrate-to-partitioned`
Migra datos de la tabla original a la particionada.

**Opciones:**
- `--batch-size=1000`: Tamaño del lote (default: 1000)
- `--dry-run`: Ejecutar sin migrar datos reales

**Ejemplos:**
```bash
# Dry run
php artisan revisions:migrate-to-partitioned --dry-run

# Migración con lotes de 500
php artisan revisions:migrate-to-partitioned --batch-size=500

# Migración normal
php artisan revisions:migrate-to-partitioned
```

### `revisions:analyze-partitions`
Analiza las particiones y rendimiento.

**Opciones:**
- `--performance`: Ejecutar tests de rendimiento
- `--model=ModelName`: Probar modelo específico

**Ejemplos:**
```bash
# Análisis básico
php artisan revisions:analyze-partitions

# Con tests de rendimiento
php artisan revisions:analyze-partitions --performance

# Test específico para Building
php artisan revisions:analyze-partitions --performance --model="App\Models\Building"
```

## 📋 Estructura de Particiones

La tabla se particiona por `revisionable_type` con las siguientes particiones:

| Partición | Modelo |
|-----------|--------|
| `p_building` | `App\Models\Building` |
| `p_building_contact` | `App\Models\BuildingsRelationships\BuildingContact` |
| `p_user_contact` | `App\Models\User\Contact` |
| `p_user` | `App\User` |
| `p_user_user` | `App\Models\User\User` |
| `p_building_intercom` | `App\Models\BuildingIntercom` |
| `p_service` | `App\Models\Service` |
| `p_flat` | `App\Models\Flat` |
| `p_case` | `App\Models\Caseq` |
| `p_other` | Otros modelos (DEFAULT) |

## ⚡ Beneficios Esperados

1. **Consultas más rápidas**: MySQL solo busca en la partición relevante
2. **Índices más eficientes**: Índices más pequeños por partición
3. **Mantenimiento optimizado**: Operaciones de mantenimiento por partición
4. **Escalabilidad**: Mejor rendimiento con crecimiento de datos

## 🔄 Rollback

Si necesitas revertir los cambios:

```bash
# Rollback del switch
php artisan migrate:rollback

# Esto restaurará:
# - revisions_backup -> revisions
# - revisions -> revisions_partitioned
```

## 🗂️ Archivos Creados

### Migraciones
- `database/migrations/2024_12_20_000001_create_partitioned_revisions_table.php`
- `database/migrations/2024_12_20_000002_switch_to_partitioned_revisions.php`

### Comandos
- `app/Console/Commands/MigrateRevisionsToPartitioned.php`
- `app/Console/Commands/AnalyzeRevisionsPartitions.php`

### Scripts
- `scripts/setup_revisions_partitioning.sh`

## ⚠️ Consideraciones Importantes

1. **Backup**: Siempre haz backup antes de ejecutar en producción
2. **Downtime**: El proceso puede requerir downtime mínimo durante el switch
3. **Espacio**: Temporalmente necesitarás espacio para ambas tablas
4. **Testing**: Prueba primero en desarrollo/staging

## 🧪 Testing

### Verificar funcionamiento básico
```bash
# Verificar que las revisiones siguen funcionando
php artisan tinker --execute="
\$building = App\Models\Building::first();
echo 'Revisiones del building: ' . \$building->revisionHistory()->count();
"
```

### Test de rendimiento
```bash
# Comparar rendimiento antes y después
php artisan revisions:analyze-partitions --performance
```

## 📞 Soporte

Si encuentras problemas:

1. Verifica logs: `storage/logs/laravel.log`
2. Ejecuta análisis: `php artisan revisions:analyze-partitions`
3. Verifica integridad: Compara conteos entre tablas

## 🎉 Resultado Final

Después de la implementación exitosa:
- ✅ Tabla `revisions` particionada y optimizada
- ✅ Backup seguro en `revisions_backup`
- ✅ Rendimiento mejorado en consultas
- ✅ Comandos de análisis disponibles
- ✅ Posibilidad de rollback si es necesario
