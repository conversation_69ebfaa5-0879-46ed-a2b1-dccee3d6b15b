<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Create the new partitioned table
        DB::statement("
            CREATE TABLE revisions_partitioned (
                id INT UNSIGNED AUTO_INCREMENT,
                revisionable_type VARCHAR(255) NOT NULL,
                revisionable_id VARCHAR(255) NOT NULL,
                user_id INT NULL,
                `key` VARCHAR(255) NOT NULL,
                old_value TEXT NULL,
                new_value TEXT NULL,
                created_at TIMESTAMP NULL,
                updated_at TIMESTAMP NULL,
                PRIMARY KEY (id, revisionable_type),
                INDEX idx_revisionable (revisionable_id, revisionable_type),
                INDEX idx_user_date (user_id, created_at),
                INDEX idx_created_at (created_at),
                INDEX idx_lookup (revisionable_type, revisionable_id, created_at)
            ) ENGINE=InnoDB
            PARTITION BY LIST COLUMNS(revisionable_type) (
                PARTITION p_building VALUES IN ('App\\\\Models\\\\Building'),
                PARTITION p_building_contact VALUES IN ('App\\\\Models\\\\BuildingsRelationships\\\\BuildingContact'),
                PARTITION p_user_contact VALUES IN ('App\\\\Models\\\\User\\\\Contact'),
                PARTITION p_user VALUES IN ('App\\\\User'),
                PARTITION p_user_user VALUES IN ('App\\\\Models\\\\User\\\\User'),
                PARTITION p_building_intercom VALUES IN ('App\\\\Models\\\\BuildingIntercom'),
                PARTITION p_flat VALUES IN ('App\\\\Models\\\\Flat'),
                PARTITION p_case VALUES IN ('App\\\\Models\\\\Caseq'),
                PARTITION p_service VALUES IN ('App\\\\Models\\\\Service'),
                PARTITION p_category VALUES IN ('App\\\\Models\\\\Category'),
                PARTITION p_tag VALUES IN ('App\\\\Models\\\\Tag'),
                PARTITION p_administration VALUES IN ('App\\\\Models\\\\Company\\\\Administration'),
                PARTITION p_temporary_contact VALUES IN ('App\\\\Models\\\\TemporaryContact'),
                PARTITION p_other VALUES IN ('App\\\\Models\\\\Area', 'App\\\\Models\\\\Other')
            )
        ");

        // Step 2: Copy all data from original table to partitioned table
        DB::statement("
            INSERT INTO revisions_partitioned 
            (id, revisionable_type, revisionable_id, user_id, `key`, old_value, new_value, created_at, updated_at)
            SELECT id, revisionable_type, revisionable_id, user_id, `key`, old_value, new_value, created_at, updated_at
            FROM revisions
        ");

        // Step 3: Verify data integrity
        $originalCount = DB::table('revisions')->count();
        $partitionedCount = DB::table('revisions_partitioned')->count();
        
        if ($originalCount !== $partitionedCount) {
            throw new \Exception(
                "Data migration failed: Original table has {$originalCount} records, " .
                "partitioned table has {$partitionedCount} records."
            );
        }

        // Step 4: Rename tables to make the switch
        DB::statement('RENAME TABLE revisions TO revisions_backup');
        DB::statement('RENAME TABLE revisions_partitioned TO revisions');

        echo "✅ Revisions table successfully partitioned!\n";
        echo "📊 Migrated {$originalCount} records\n";
        echo "🗂️ Original table backed up as 'revisions_backup'\n";
        echo "⚡ Performance should be significantly improved\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if backup table exists
        if (!Schema::hasTable('revisions_backup')) {
            throw new \Exception('Cannot rollback: revisions_backup table not found');
        }

        // Rename current partitioned table
        DB::statement('RENAME TABLE revisions TO revisions_partitioned');
        
        // Restore the original table
        DB::statement('RENAME TABLE revisions_backup TO revisions');

        // Drop the partitioned table
        Schema::dropIfExists('revisions_partitioned');

        echo "🔄 Rolled back to original revisions table\n";
    }
};
