<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Verify that the partitioned table has data
        $originalCount = DB::table('revisions')->count();
        $partitionedCount = DB::table('revisions_partitioned')->count();

        if ($originalCount !== $partitionedCount) {
            throw new \Exception(
                "Cannot switch tables: record counts don't match. " .
                "Original: {$originalCount}, Partitioned: {$partitionedCount}. " .
                "Please run 'php artisan revisions:migrate-to-partitioned' first."
            );
        }

        // Rename the original table to backup
        DB::statement('RENAME TABLE revisions TO revisions_backup');
        
        // Rename the partitioned table to the original name
        DB::statement('RENAME TABLE revisions_partitioned TO revisions');

        echo "Successfully switched to partitioned revisions table.\n";
        echo "Original table backed up as 'revisions_backup'.\n";
        echo "You can drop the backup table later with: DROP TABLE revisions_backup;\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if backup table exists
        if (!Schema::hasTable('revisions_backup')) {
            throw new \Exception('Cannot rollback: revisions_backup table not found');
        }

        // Rename current partitioned table
        DB::statement('RENAME TABLE revisions TO revisions_partitioned');
        
        // Restore the original table
        DB::statement('RENAME TABLE revisions_backup TO revisions');

        echo "Rolled back to original revisions table.\n";
        echo "Partitioned table available as 'revisions_partitioned'.\n";
    }
};
