<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the new partitioned table
        DB::statement("
            CREATE TABLE revisions_partitioned (
                id INT UNSIGNED AUTO_INCREMENT,
                revisionable_type VARCHAR(255) NOT NULL,
                revisionable_id VARCHAR(255) NOT NULL,
                user_id INT NULL,
                `key` VARCHAR(255) NOT NULL,
                old_value TEXT NULL,
                new_value TEXT NULL,
                created_at TIMESTAMP NULL,
                updated_at TIMESTAMP NULL,
                PRIMARY KEY (id, revisionable_type),
                INDEX idx_revisionable (revisionable_id, revisionable_type),
                INDEX idx_user_date (user_id, created_at),
                INDEX idx_created_at (created_at),
                INDEX idx_lookup (revisionable_type, revisionable_id, created_at)
            ) ENGINE=InnoDB
            PARTITION BY LIST COLUMNS(revisionable_type) (
                PARTITION p_building VALUES IN ('App\\\\Models\\\\Building'),
                PARTITION p_building_contact VALUES IN ('App\\\\Models\\\\BuildingsRelationships\\\\BuildingContact'),
                PARTITION p_user_contact VALUES IN ('App\\\\Models\\\\User\\\\Contact'),
                PARTITION p_user VALUES IN ('App\\\\User'),
                PARTITION p_user_user VALUES IN ('App\\\\Models\\\\User\\\\User'),
                PARTITION p_building_intercom VALUES IN ('App\\\\Models\\\\BuildingIntercom'),
                PARTITION p_service VALUES IN ('App\\\\Models\\\\Service'),
                PARTITION p_flat VALUES IN ('App\\\\Models\\\\Flat'),
                PARTITION p_case VALUES IN ('App\\\\Models\\\\Caseq'),
                PARTITION p_other VALUES IN (DEFAULT)
            )
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('revisions_partitioned');
    }
};
