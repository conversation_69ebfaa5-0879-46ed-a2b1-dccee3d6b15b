#!/bin/bash

# Script para configurar el particionamiento de la tabla revisions
# Autor: Augment Agent
# Fecha: 2024-12-20

set -e  # Exit on any error

echo "🚀 Configurando particionamiento de tabla revisions..."
echo "=================================================="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para mostrar mensajes
info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar que estamos en el directorio correcto
if [ ! -f "artisan" ]; then
    error "Este script debe ejecutarse desde el directorio raíz de <PERSON>vel"
    exit 1
fi

# Paso 1: Verificar estado actual
info "Paso 1: Verificando estado actual de la tabla revisions..."
php artisan tinker --execute="
echo 'Registros en tabla revisions: ' . DB::table('revisions')->count() . PHP_EOL;
echo 'Distribución por tipo:' . PHP_EOL;
DB::table('revisions')->select('revisionable_type', DB::raw('COUNT(*) as count'))
    ->groupBy('revisionable_type')
    ->orderBy('count', 'desc')
    ->get()
    ->each(function(\$item) { 
        echo '  ' . \$item->revisionable_type . ': ' . \$item->count . PHP_EOL; 
    });
"

# Paso 2: Ejecutar migración para crear tabla particionada
info "Paso 2: Creando tabla particionada..."
php artisan migrate --path=database/migrations/2024_12_20_000001_create_partitioned_revisions_table.php
success "Tabla particionada creada"

# Paso 3: Migrar datos (dry run primero)
info "Paso 3: Ejecutando migración de datos (dry run)..."
php artisan revisions:migrate-to-partitioned --dry-run
read -p "¿Continuar con la migración real? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    info "Migrando datos reales..."
    php artisan revisions:migrate-to-partitioned --batch-size=1000
    success "Datos migrados exitosamente"
else
    warning "Migración cancelada por el usuario"
    exit 0
fi

# Paso 4: Verificar migración
info "Paso 4: Verificando migración..."
php artisan revisions:analyze-partitions

# Paso 5: Hacer el switch (opcional)
warning "ATENCIÓN: El siguiente paso cambiará la tabla activa"
echo "Esto renombrará:"
echo "  - revisions -> revisions_backup"
echo "  - revisions_partitioned -> revisions"
echo ""
read -p "¿Hacer el switch a la tabla particionada? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    info "Haciendo switch a tabla particionada..."
    php artisan migrate --path=database/migrations/2024_12_20_000002_switch_to_partitioned_revisions.php
    success "Switch completado exitosamente"
    
    # Verificar que todo funciona
    info "Verificando funcionamiento..."
    php artisan revisions:analyze-partitions --performance
    
    success "¡Particionamiento completado exitosamente!"
    echo ""
    echo "📊 Resumen:"
    echo "  - Tabla original respaldada como 'revisions_backup'"
    echo "  - Tabla particionada activa como 'revisions'"
    echo "  - Puedes eliminar el backup más tarde con: DROP TABLE revisions_backup;"
    echo ""
    echo "🔧 Comandos útiles:"
    echo "  - Analizar particiones: php artisan revisions:analyze-partitions"
    echo "  - Test de rendimiento: php artisan revisions:analyze-partitions --performance"
    echo "  - Rollback: php artisan migrate:rollback"
    
else
    warning "Switch cancelado. La tabla particionada está lista pero no activa."
    echo "Para activarla más tarde ejecuta:"
    echo "  php artisan migrate --path=database/migrations/2024_12_20_000002_switch_to_partitioned_revisions.php"
fi

echo ""
success "Proceso completado!"
