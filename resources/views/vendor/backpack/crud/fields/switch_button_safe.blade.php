<div class="form-check form-switch" style="left: 16px; margin-bottom: 5px;">

    <input type="hidden" name="{{$field['name']}}" value="0">

    <input name="{{$field['name']}}"
           value="{{ old(square_brackets_to_dots($field['name'])) ?? $field['value'] ?? $field['default'] ?? "0" }}"
           class="bool_fields_back form-check-input" type="checkbox" id="{{$field['name']}}"
    >
    <label class="form-check-label" style="" for="{{$field['name']}}">{{$field['label']}}</label>
</div>

<style>
    @media (max-width: 1638px) {
        .min-view {
            display: none !important;
        }
    }
</style>

<script>
    $(document).ready(() => {
        let pressedSwitchAppFoxsys = $('#app_foxsys').val() ?? 0

        if (pressedSwitchAppFoxsys == '1')
            $('#app_foxsys').click()

        $('#app_foxsys').on('change', () => {
            $('#app_foxsys').val(pressedSwitchAppFoxsys == 1 ? 0 : 1)
            pressedSwitchAppFoxsys = $('#app_foxsys').val()
        })
    })

</script>
