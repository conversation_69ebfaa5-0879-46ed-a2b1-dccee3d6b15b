<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class MigrateRevisionsToPartitioned extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revisions:migrate-to-partitioned 
                            {--batch-size=1000 : Number of records to process in each batch}
                            {--dry-run : Run without actually migrating data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing revisions data to the new partitioned table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $batchSize = (int) $this->option('batch-size');
        $dryRun = $this->option('dry-run');

        $this->info('Starting revisions migration to partitioned table...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No data will be actually migrated');
        }

        // Check if tables exist
        if (!$this->checkTables()) {
            return 1;
        }

        // Get total count
        $totalRecords = DB::table('revisions')->count();
        $this->info("Total records to migrate: {$totalRecords}");

        if ($totalRecords === 0) {
            $this->info('No records to migrate.');
            return 0;
        }

        // Clear the partitioned table if it has data
        if (!$dryRun) {
            DB::table('revisions_partitioned')->truncate();
            $this->info('Cleared existing data from partitioned table');
        }

        $processed = 0;
        $errors = 0;
        $bar = $this->output->createProgressBar($totalRecords);
        $bar->start();

        try {
            DB::table('revisions')
                ->orderBy('id')
                ->chunk($batchSize, function ($revisions) use (&$processed, &$errors, $bar, $dryRun) {
                    $insertData = [];
                    
                    foreach ($revisions as $revision) {
                        $insertData[] = [
                            'id' => $revision->id,
                            'revisionable_type' => $revision->revisionable_type,
                            'revisionable_id' => $revision->revisionable_id,
                            'user_id' => $revision->user_id,
                            'key' => $revision->key,
                            'old_value' => $revision->old_value,
                            'new_value' => $revision->new_value,
                            'created_at' => $revision->created_at,
                            'updated_at' => $revision->updated_at,
                        ];
                    }

                    if (!$dryRun && !empty($insertData)) {
                        try {
                            DB::table('revisions_partitioned')->insert($insertData);
                        } catch (\Exception $e) {
                            $errors += count($insertData);
                            Log::error('Error migrating batch: ' . $e->getMessage());
                            $this->error("\nError migrating batch: " . $e->getMessage());
                        }
                    }

                    $processed += count($revisions);
                    $bar->advance(count($revisions));
                });

        } catch (\Exception $e) {
            $bar->finish();
            $this->error("\nMigration failed: " . $e->getMessage());
            Log::error('Revisions migration failed: ' . $e->getMessage());
            return 1;
        }

        $bar->finish();
        $this->newLine();

        if ($dryRun) {
            $this->info("DRY RUN completed. Would have processed {$processed} records.");
        } else {
            $this->info("Migration completed successfully!");
            $this->info("Processed: {$processed} records");
            
            if ($errors > 0) {
                $this->warn("Errors: {$errors} records failed to migrate");
            }

            // Verify the migration
            $this->verifyMigration();
        }

        return 0;
    }

    /**
     * Check if required tables exist
     */
    private function checkTables(): bool
    {
        if (!Schema::hasTable('revisions')) {
            $this->error('Source table "revisions" does not exist');
            return false;
        }

        if (!Schema::hasTable('revisions_partitioned')) {
            $this->error('Target table "revisions_partitioned" does not exist. Run the migration first.');
            return false;
        }

        return true;
    }

    /**
     * Verify the migration was successful
     */
    private function verifyMigration(): void
    {
        $originalCount = DB::table('revisions')->count();
        $migratedCount = DB::table('revisions_partitioned')->count();

        $this->info("Verification:");
        $this->info("Original table: {$originalCount} records");
        $this->info("Partitioned table: {$migratedCount} records");

        if ($originalCount === $migratedCount) {
            $this->info("✅ Migration verification successful!");
        } else {
            $this->error("❌ Migration verification failed! Record counts don't match.");
        }

        // Show partition distribution
        $this->info("\nPartition distribution:");
        $partitionData = DB::select("
            SELECT 
                revisionable_type,
                COUNT(*) as count
            FROM revisions_partitioned 
            GROUP BY revisionable_type 
            ORDER BY count DESC
        ");

        foreach ($partitionData as $partition) {
            $this->info("  {$partition->revisionable_type}: {$partition->count}");
        }
    }
}
