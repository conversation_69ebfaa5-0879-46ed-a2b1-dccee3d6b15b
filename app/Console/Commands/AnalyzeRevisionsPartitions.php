<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AnalyzeRevisionsPartitions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'revisions:analyze-partitions 
                            {--performance : Run performance tests}
                            {--model= : Test specific model type}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze revisions table partitions and performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Analyzing revisions table partitions...');

        // Check if table is partitioned
        if (!$this->isTablePartitioned()) {
            $this->error('The revisions table is not partitioned yet.');
            return 1;
        }

        // Show partition information
        $this->showPartitionInfo();

        // Show data distribution
        $this->showDataDistribution();

        // Run performance tests if requested
        if ($this->option('performance')) {
            $this->runPerformanceTests();
        }

        return 0;
    }

    /**
     * Check if the revisions table is partitioned
     */
    private function isTablePartitioned(): bool
    {
        $result = DB::select("
            SELECT COUNT(*) as partition_count
            FROM INFORMATION_SCHEMA.PARTITIONS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'revisions' 
            AND PARTITION_NAME IS NOT NULL
        ");

        return $result[0]->partition_count > 0;
    }

    /**
     * Show partition information
     */
    private function showPartitionInfo(): void
    {
        $this->info("\n📊 Partition Information:");
        
        $partitions = DB::select("
            SELECT 
                PARTITION_NAME,
                PARTITION_EXPRESSION,
                PARTITION_DESCRIPTION,
                TABLE_ROWS,
                AVG_ROW_LENGTH,
                DATA_LENGTH,
                INDEX_LENGTH
            FROM INFORMATION_SCHEMA.PARTITIONS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'revisions' 
            AND PARTITION_NAME IS NOT NULL
            ORDER BY PARTITION_NAME
        ");

        $headers = ['Partition', 'Description', 'Rows', 'Avg Row Length', 'Data Size', 'Index Size'];
        $rows = [];

        foreach ($partitions as $partition) {
            $rows[] = [
                $partition->PARTITION_NAME,
                $partition->PARTITION_DESCRIPTION,
                number_format($partition->TABLE_ROWS),
                $this->formatBytes($partition->AVG_ROW_LENGTH),
                $this->formatBytes($partition->DATA_LENGTH),
                $this->formatBytes($partition->INDEX_LENGTH),
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Show data distribution across partitions
     */
    private function showDataDistribution(): void
    {
        $this->info("\n📈 Data Distribution:");

        $distribution = DB::select("
            SELECT 
                revisionable_type,
                COUNT(*) as count,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM revisions), 2) as percentage
            FROM revisions 
            GROUP BY revisionable_type 
            ORDER BY count DESC
        ");

        $headers = ['Model Type', 'Count', 'Percentage'];
        $rows = [];

        foreach ($distribution as $item) {
            $rows[] = [
                $item->revisionable_type,
                number_format($item->count),
                $item->percentage . '%'
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Run performance tests
     */
    private function runPerformanceTests(): void
    {
        $this->info("\n⚡ Performance Tests:");

        $modelTypes = [
            'App\\Models\\Building',
            'App\\Models\\BuildingsRelationships\\BuildingContact',
            'App\\Models\\User\\Contact',
            'App\\User'
        ];

        $specificModel = $this->option('model');
        if ($specificModel) {
            $modelTypes = [$specificModel];
        }

        foreach ($modelTypes as $modelType) {
            $this->testModelPerformance($modelType);
        }
    }

    /**
     * Test performance for a specific model type
     */
    private function testModelPerformance(string $modelType): void
    {
        $this->info("\nTesting: {$modelType}");

        // Get a sample ID for this model type
        $sampleRecord = DB::table('revisions')
            ->where('revisionable_type', $modelType)
            ->first();

        if (!$sampleRecord) {
            $this->warn("  No records found for {$modelType}");
            return;
        }

        $revisionableId = $sampleRecord->revisionable_id;

        // Test 1: Get revisions for specific record (most common query)
        $start = microtime(true);
        $revisions = DB::table('revisions')
            ->where('revisionable_type', $modelType)
            ->where('revisionable_id', $revisionableId)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();
        $time1 = (microtime(true) - $start) * 1000;

        // Test 2: Count revisions for this model type
        $start = microtime(true);
        $count = DB::table('revisions')
            ->where('revisionable_type', $modelType)
            ->count();
        $time2 = (microtime(true) - $start) * 1000;

        // Test 3: Get recent revisions for this model type
        $start = microtime(true);
        $recent = DB::table('revisions')
            ->where('revisionable_type', $modelType)
            ->where('created_at', '>=', now()->subDays(30))
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();
        $time3 = (microtime(true) - $start) * 1000;

        $this->info("  📋 Get specific record revisions: " . round($time1, 2) . "ms ({$revisions->count()} records)");
        $this->info("  🔢 Count all revisions: " . round($time2, 2) . "ms ({$count} total)");
        $this->info("  📅 Get recent revisions: " . round($time3, 2) . "ms ({$recent->count()} records)");

        // Show explain for the main query
        $explain = DB::select("
            EXPLAIN SELECT * FROM revisions 
            WHERE revisionable_type = ? 
            AND revisionable_id = ? 
            ORDER BY created_at DESC 
            LIMIT 20
        ", [$modelType, $revisionableId]);

        $this->info("  🔍 Query uses: " . ($explain[0]->key ?? 'No index'));
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes): string
    {
        if ($bytes == 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor(log($bytes, 1024));
        
        return round($bytes / pow(1024, $factor), 2) . ' ' . $units[$factor];
    }
}
