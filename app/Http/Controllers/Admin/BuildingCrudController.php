<?php

namespace App\Http\Controllers\Admin;


use App\Helpers\Helper;
use App\Http\Fields\BuildingClientFields;
use App\Http\Fields\BuildingComercialFields;
use App\Http\Fields\BuildingEngineeringFields;
use App\Http\Fields\BuildingFields;
use App\Http\Requests\BuildingEngineeringRequest;
use App\Http\Requests\BuildingRequest;
use App\Http\Resources\ActiveTemporaryPermitsResource;
use App\Http\Resources\BuildingContactResource;
use App\Http\Resources\ContactInBuildingResource;
use App\Http\Resources\ContactInDashboardResource;
use App\Http\Resources\ServiceInDashboardResource;
use App\Http\Resources\ServiceResource;
use App\Jobs\CreateKazooAccountJob;
use App\Jobs\CreateUpdateKazooBuildingContactDevicesCallflowsJob;
use App\Jobs\DeleteAllCallFlowJob;
use App\Models\AccessCode;
use App\Models\Area;
use App\Models\Building;
use App\Models\BuildingsRelationships\BuildingContact;
use App\Models\BuildingsRelationships\BuildingService;
use App\Models\BuildingsRelationships\CommissionIntegrant;
use App\Models\CustomRevision;
use App\Models\Flat;
use App\Models\KazooFlatsConsecutive;
use App\Models\Roles;
use App\Models\Service;
use App\Models\Tower;
use App\Models\TransferredCalls;
use App\Models\User\User;
use App\Models\User\Contact;
use App\Notifications\ODTNotification;
use App\Notifications\OvercomeInitialDateNotification;
use App\Services\KazooService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\ReviseOperation\ReviseOperationCustom;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

/**
 * Class BuildingCrudController
 * @package App\Http\Controllers\Admin
 * @property-read CrudPanel $crud
 */
class BuildingCrudController extends CrudController
{

    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;
    use \App\Helpers\Revise\ReviseOperationCustom;
    use \App\Traits\CustomRevisionsTrait {
        \App\Traits\CustomRevisionsTrait::listRevisions insteadof \App\Helpers\Revise\ReviseOperationCustom;
    }

    private static $cadeteCommentsOnExceptions = 2;

    public function fetchService()
    {
        return $this->fetch(Service::class);
    }

    public function fetchAdministration()
    {
        return $this->fetch(\App\Models\Company\Administration::class);
    }

    public function setup()
    {
        $this->crud->setModel('App\Models\Building');
        $this->crud->setRoute(config('backpack.base.route_prefix') . '/building');
        $this->crud->setEntityNameStrings('edificio', 'edificios');
        $this->crud->allowAccess('show');

        if (!backpack_user()->hasRole(['Admin', 'Jefe Atención al Cliente', 'Jefe Ingeniería', 'Auxiliar Ingeniería'])) {
            if (backpack_user()->hasRole(['Jefe Comercial', 'Auxiliar Comercial', 'Jefe Técnico'])) {
                $this->crud->denyAccess(['reorder', 'delete', 'revise']);
            }
            if (backpack_user()->hasRole('Representante Atención al Cliente')) {
                $this->crud->denyAccess(['reorder', 'delete', 'revise']);
            } elseif (backpack_user()->hasRole('Jefe Técnico')) {
                $this->crud->denyAccess(['create', 'reorder', 'delete', 'revise', 'lead']);
            } else {
                $this->crud->denyAccess(['create', 'update', 'reorder', 'delete', 'revise', 'lead']);
            }
        }
        if (!backpack_user()->hasRole(['Jefe Administrativo', 'Jefe Comercial', 'Auxiliar Comercial', 'Jefe Técnico', 'Auxiliar Administrativo'])) {
            $this->crud->addButtonFromView('line', 'viewLogs', 'buildingLogs', 'end');
        }
        $this->crud->setCreateContentClass('col-md-12');
        $this->crud->setEditContentClass('col-md-12');

        if (backpack_user()->hasRole(['Jefe Atención al Cliente', 'Admin', 'Representante Atención al Cliente', 'Jefe Operaciones'])) {
            $this->crud->enableExportButtons();
        }

    }


    public function cadeteHasExceptions($building, $field, $fieldComment)
    {
        return $building->$field == 2 ? $building->$fieldComment : null;
    }

    public function store(BuildingRequest $request)
    {
        $request->request->add(['creator_user_id' => Auth::user()]);

        $building = new Building();

        $building->fill($request->all());
        $warranty_date['equipament'] = isset($_POST['warranty_date']['equipament']) ? $_POST['warranty_date']['equipament'] : NULL;
        $warranty_date['other'] = isset($_POST['warranty_date']['other']) ? $_POST['warranty_date']['other'] : NULL;
        $building->warranty_date = json_encode($warranty_date);
        $building->porter = null;

        if ($building->delivery == 0) {
            $building->delivery_comment = 'No permite delivery';
        } elseif ($building->delivery == 1) {
            $building->delivery_comment = 'Si permite delivery';
        }

        // Cadete section
        $building->cadete_hall_comments = $this->cadeteHasExceptions($building, 'cadete_hall', 'cadete_hall_comments');
        $building->cadete_up_comments = $this->cadeteHasExceptions($building, 'cadete_up', 'cadete_up_comments');


        if (!array_key_exists('video_disabled', $request->all())) {
            $building->video_disabled = 0;
        }
        if (!array_key_exists('open_door_disabled', $request->all())) {
            $building->open_door_disabled = 0;
        }
        if (!array_key_exists('door_special_denomination', $request->all())) {
            $building->door_special_denomination = 'No';
        }

        if (!array_key_exists('face_recognition', $request->all())) {
            $building->face_recognition = 0;
        }

        if (!array_key_exists('camera_anpr', $request->all())) {
            $building->camera_anpr = 0;
        }

        $building->created_by = backpack_user()->id;

        $building->cleaningServices()->sync($request->cleaningServices);
        $building->physicalResponse()->sync($request->physicalResponse);
        $building->physical_response = $request->physical_response;
        $building->save();
        $building_id = $building->id;

        $towers = json_decode($request->tower_info);
        if ($towers) {
            if (sizeof($towers) > 1 || $building->door_special_denomination == 'Si') {
                Tower::createTower($request, $building->id, $towers);
            } else {
                $towers[0]->lock = $towers[0]->lock == '' ? null : $towers[0]->lock;
                $building->fill((array)$towers[0]);

                if ($building->door_special_denomination == "No") {
                    $towers[0]->id = null;
                }
                $flats = array_unique(explode(',', $towers[0]->flats));
                foreach ($flats as $flat) {
                    if ($flat != null && $flat != '') {
                        $f = new Flat();
                        $f->number = $flat;
                        $f->building_id = $building_id;
                        $f->tower_id = $towers[0]->id;
                        $f->save();
                    }
                }
            }
        }

        if ($request->porter) {
            $building->porter = json_encode($request->porter);
        }

        BuildingService::createBuildingService($request->services, $building_id);

        $building->services()->sync($request->services);

        if ($building->doors_quantity == '') {
            $building->doors_quantity = null;
        }

        $building->incoming_call = $building->intercoms->whereIn('model', \App\Models\BuildingIntercom::$kazooModels)->count() > 0 && request()->input('incoming_call', false);

        $building->save();

        $building->updateFlats();

        $this->sendNotifyForODT($building, null, null);
        \Alert::add('success', '<strong>Acción completada con exito</strong><br>Su edificio fue creado.')->flash();

        \Artisan::call('queue:restart');
        CreateKazooAccountJob::dispatchIf($building->incoming_call, $building);

        return redirect('/admin/building/' . $building->id . '/info');
    }


    public function update(BuildingRequest $request, $id)
    {
        $towers = json_decode($request->tower_info);
        $building = Building::findOrFail($id);
        $rev_security2 = $building->rev_security2;
        $rev_security3 = $building->rev_security3;
        $rev_security4 = $building->rev_security4;
        $old_building = Building::findOrFail($id);

        $old_services = $building->services()->select('services.id')->pluck('services.id')->toArray();

        $building->fill($request->all());
        $building->physical_response = $request->physical_response;
        $warranty_date['equipament'] = isset($_POST['warranty_date']['equipament']) ? $_POST['warranty_date']['equipament'] : NULL;
        $warranty_date['other'] = isset($_POST['warranty_date']['other']) ? $_POST['warranty_date']['other'] : NULL;
        $building->warranty_date = !backpack_user()->hasRole(['Auxiliar Ingeniería', 'Jefe Ingeniería']) ? json_encode($warranty_date) : json_encode($building->getOriginal('warranty_date'));
        if ($building->delivery == 0) {
            $building->delivery_comment = 'No permite delivery';
        } elseif ($building->delivery == 1) {
            $building->delivery_comment = 'Si permite delivery';
        }

        // Cadete section
        if ($building->cadete_hall != 2) {
            $building->cadete_hall_comments = null;
        }
        if ($building->cadete_up != 2) {
            $building->cadete_up_comments = null;
        }

        $building->incoming_call = $building->intercoms->whereIn('model', \App\Models\BuildingIntercom::$kazooModels)->count() > 0 && request()->input('incoming_call', false);


        if (!array_key_exists('video_disabled', $request->all())) {
            $building->video_disabled = 0;
        }
        if (!array_key_exists('open_door_disabled', $request->all())) {
            $building->open_door_disabled = 0;
        }
        if (!array_key_exists('door_special_denomination', $request->all())) {
            $building->door_special_denomination = 'No';
        }
        if (!array_key_exists('face_recognition', $request->all())) {
            $building->face_recognition = 0;
        }

        if (!array_key_exists('camera_anpr', $request->all())) {
            $building->camera_anpr = 0;
        }

        foreach ($building->referents as $user_referent) {
            $building_user_referent = BuildingContact::where('contact_id', $user_referent->id)->first();
            $building_user_referent->referrer = 0;
            $building_user_referent->update();
        }

        foreach ($request->referents ?? [] as $referent_id) {
            $building_user_referent = BuildingContact::where('contact_id', $user_referent->id)->first();
            $building_user_referent->referrer = 1;
            $building_user_referent->update();
        }

        self::saveBuildingFiles('rev_security2', $rev_security2, $building, 'uploads/rev_security2');
        self::saveBuildingFiles('rev_security3', $rev_security3, $building, 'uploads/rev_security3');
        self::saveBuildingFiles('rev_security4', $rev_security4, $building, 'uploads/rev_security4');

        if ($towers) {
            if (sizeof($towers) > 1 || $building->door_special_denomination == 'Si') {
                Tower::UpdateTower($request, $building->id, $towers);
            } else {
                $towers[0]->lock = $towers[0]->lock == '' ? null : $towers[0]->lock;

                if ($building->door_special_denomination == "No") {
                    $towers[0]->id = null;
                    $towers[0]->doors_quantity = 1;
                }

                $building->fill((array)$towers[0]);

                if (!$towers[0]->id) {
                    Tower::where('building_id', $building->id)->delete();

                    Flat::query()
                        ->where('building_id', $building->id)
                        ->update([
                            'tower_id' => null
                        ]);

                }

                $flats_of_building = json_decode(Flat::where('building_id', $building->id)->pluck('number'));
                $flats = array_unique(explode(',', $towers[0]->flats));
                $flatsToCreate = array_diff($flats, $flats_of_building);
                foreach ($flatsToCreate as $flat) {
                    Flat::query()->create([
                        'building_id' => $building->id,
                        'number' => $flat,
                        'tower_id' => $towers[0]->id,
                    ]);
                }

                $diference_flat = array_diff($flats_of_building, $flats);
            }
        }

        if ($request->porter) {
            $building->porter = json_encode($request->porter);
        }

        $building->updated_by = backpack_user()->id;

        if ($building->allows_elevator_comunication != 0 or $building->allows_elevator_comunication != 1) {
            $building->allows_elevator_comunication = 0;
        }

        if ($building->doors_quantity == '') {
            $building->doors_quantity = null;
        }

        $building->cleaningServices()->sync($request->cleaningServices);
        $building->physicalResponse()->sync($request->physicalResponse);

        $this->saveServicesRevisionInformation($request, $old_services, $building);

        $buildingOriginalName = $building->getOriginal('name');
        $building->update();

        $building->updateFlats();

        CommissionIntegrant::updateCommissions(json_encode($request->commissions), $building->id);

        $this->sendNotifyForODT($building, $old_building->order_job, $old_building->order_job_checked);

        \Alert::add('success', '<strong>Acción completada con exito</strong><br>Su edificio fue actualizado.')->flash();
        \Artisan::call('queue:restart');

        CreateKazooAccountJob::dispatch($building, $buildingOriginalName);

        return redirect('/admin/building/' . $building->id . '/info');
    }

    private function saveServicesRevisionInformation($request, $old_services, Building $building)
    {
        if ($request->services) {
            $new_services = is_array($request->services) ? $request->services : [];

            $services_changed = count(array_diff($old_services, $new_services)) > 0 ||
                count(array_diff($new_services, $old_services)) > 0;

            if ($services_changed) {
                $oldServiceNames = Service::whereIn('services.id', $old_services)
                    ->select('services.id', 'services.service', 'services.provider')
                    ->get()
                    ->map(function($service) {
                        return $service->service . ' - ' . $service->provider;
                    })
                    ->implode(', ');

                $newServiceNames = Service::whereIn('services.id', $new_services)
                    ->select('services.id', 'services.service', 'services.provider')
                    ->get()
                    ->map(function($service) {
                        return $service->service . ' - ' . $service->provider;
                    })
                    ->implode(', ');

                CustomRevision::create([
                    'revisionable_type' => get_class($building),
                    'revisionable_id' => $building->id,
                    'key' => 'services',
                    'old_value' => $oldServiceNames ?: 'Sin servicios',
                    'new_value' => $newServiceNames ?: 'Sin servicios',
                    'user_id' => backpack_user()->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            $building->services()->sync($new_services);
        }
    }

    private static function saveBuildingFiles($field, $oldData, Building $building, $path, $disk = 'uploads')
    {
        if (request()->hasFile($field)) {
            if ($oldData) {
                \Storage::disk($disk)->delete($oldData);
            }
            $filename = $field . '-' . $building->building_number . '-' . Carbon::now()->format('Ymds') . '.' . request()->file($field)->getClientOriginalExtension();
            $filepath = request()->file($field)->storeAs($path, $filename, $disk);
            $building->$field = $filepath;
        }
    }

    /**
     * @throws \Exception
     */
    public static function createOrUpdateKazooAccount(Building $building, string $buildingOriginalName)
    {
        self::findKazooAccountForCurrentBuildingAndUpdateBuildingData($building);
        if (!is_null($building->incoming_call) && $building->incoming_call) {
            if (!$building->account_kazoo_id) {
                self::createKazooAccount($building);
            } else if (strtolower(trim($building->name)) != strtolower(trim($buildingOriginalName))) {
                self::
                updateKazooAccount($building);
            }
            \Artisan::call('queue:restart');
            CreateUpdateKazooBuildingContactDevicesCallflowsJob::dispatch($building);
        } else {
            $allBuildingContact = $building->contacts();
            $allBuildingContact->update(['intercom_incoming_call' => false]);
            foreach ($allBuildingContact->get() as $buildingContact) {
                $buildingContact->createDeleteIntercomContact($buildingContact);
            }
            \Artisan::call('queue:restart');
            DeleteAllCallFlowJob::dispatch($building);
        }
    }

    /**
     * @throws \Exception
     */
    public static function createKazooAccount(Building $building)
    {
        // Create Kazoo account
        $kazoo = KazooService::createAccount($building->building_number);
        if (count($kazoo) > 0 && $kazoo['success'] === true) {
            $building->update([
                'account_kazoo' => $kazoo['account_kazoo'],
                'domain_kazoo' => $kazoo['domain_kazoo'],
                'account_kazoo_id' => $kazoo['account_kazoo_id']
            ]);
        } else {
            \Alert::add('warning', '<strong>Kazoo</strong><br>Edificio creado correctamente, central telefonica inalcanzable, comunicarse con soporte tecnico.')->flash();
        }
    }

    /**
     * @throws \Exception
     */
    public static function updateKazooAccount(Building $building)
    {
        // Modificar los datos del edificio
        $kazoo = KazooService::updateAccount($building->account_kazoo_id);
        if (count($kazoo) > 0 && $kazoo['success'] === true) {
            $building->update([
                'account_kazoo' => $kazoo['account_kazoo'],
            ]);
        } else {
            \Alert::add('warning', '<strong>Kazoo</strong><br>Edificio creado correctamente, central telefonica inalcanzable, comunicarse con soporte tecnico.')->flash();
        }
    }

    /**
     * @throws \Exception
     */
    public static function findKazooAccountForCurrentBuildingAndUpdateBuildingData(Building $building)
    {
        $accountKazoo = KazooService::getAccount(md5($building->building_number));
        if (count($accountKazoo) > 0 && $accountKazoo['success'] === true) {
            $building->update([
                'account_kazoo' => $accountKazoo['account_kazoo'],
                'domain_kazoo' => $accountKazoo['domain_kazoo'],
                'account_kazoo_id' => $accountKazoo['account_kazoo_id']
            ]);
        } else {
            $building->update([
                'account_kazoo' => null,
                'domain_kazoo' => null,
                'account_kazoo_id' => null
            ]);
        }
    }

    public static function createBuildingContactsDevices($building)
    {
        $building->contacts()
            ->whereNotNull('flat_id')
            ->whereIn('contact_type', Contact::$appFoxsysUsers)->each(/**
             * @throws \Exception
             */ function (BuildingContact $buildingContact) {
                $kazooDevice = KazooService::createOrUpdateDevice($buildingContact->contact_id, $buildingContact->flat_id);

                if (isset($kazooDevice['success']) && $kazooDevice['success'] === true) {
                    $buildingContact->name_kazoo = $kazooDevice['name_kazoo'];
                    $buildingContact->user_sip_kazoo = $kazooDevice['username'];
                    $buildingContact->password_kazoo = $kazooDevice['password'];
                    $buildingContact->kazoo_device_id = $kazooDevice['device_id'];
                    $buildingContact->callflow_id = $kazooDevice['callflow_id'];
                    $buildingContact->save();

                    KazooFlatsConsecutive::saveData($buildingContact->contact_id, $buildingContact->flat_id, $kazooDevice);
                }
            });
    }

    protected function setupShowOperation()
    {
        $this->crud->setEntityNameStrings('edificio llamando', 'edificio llamando');

        $fields = new BuildingFields();
        $this->crud->addColumns($fields->getShowFields());
        $this->autoSetupShowOperation();

    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');

        $flats = Flat::where('building_id', $id)->get();
        foreach ($flats as $flat) {
            $flat->delete();
        }

        $olds = Tower::where('building_id', $id)->get();
        foreach ($olds as $old) {
            $old->delete();
        }

        // get entry ID from Request (makes sure its the last ID for nested resources)
        $id = $this->crud->getCurrentEntryId() ?? $id;
        $building = Building::findOrFail($id);
        $building->building_number = null;
        $building->save();
        return $this->crud->delete($id);
    }

    public function delete($id)
    {
        $building = Building::findOrFail($id);
        return $building->delete();
    }

    public function showInfo($id)
    {

        $id = $this->resolveBuildingIdFrom3cxExtensionMarigotOrFoxsys($id);

        $building = Building::where('id', $id)->with('casesTracings', 'flats')
            ->with('flats')
            ->first();
        return view('buildings.show.main', compact(['building']));
    }

    public function resolveBuildingIdFrom3cxExtensionMarigotOrFoxsys($id)
    {

        return $id == config('constants.foxsys_3cx_extension')
            ? config('constants.foxsys_building_id')
            : $id;
    }

    public function showInfoWithNumber($id)
    {
        $building_number = self::getBuildingNumberFromTheExtensionOf3xc($id);

        if (in_array($id, Config('constants.bots_extension_3cx'))) {
            $building_number = TransferredCalls::where('extension_3cx_bot', $id)->where('answered', false)->first()->building_number;
        }
        $building = Building::where('building_number', $building_number)
            ->with('flats')
            ->first();
        return view('buildings.show.main', compact(['building']));
    }

    public static function getBuildingNumberFromTheExtensionOf3xc($extension)
    {
        if ($extension == Config('constants.foxsys_3cx_extension')) return 0;
        return substr($extension, 0, 4);
    }

    public function changeOfLead($id)
    {
        $building = Building::findOrFail($id);
        $number = Building::where('lead', null)->orderBy('building_number', 'DESC')->first()->building_number + 1;
        $building->lead = null;
        $building->building_number = $number;
        $building->save();

        return redirect('/admin/building');

    }

    public function buildingInfo($id)
    {
        return (string)Building::findOrFail($id);
    }

    public function buildingInfoByName($name)
    {
        return (string)Building::where('name', $name)->pluck('building_type');
    }

    public function buildingInfoById($id)
    {
        $building = Building::query()->where('id', $id);
        return response()->json([
            'building_type' => $building->value('building_type'),
            'building_number' => $building->value('building_number'),
            'building_name' => $building->value('name'),
        ]);
    }

    public function initializeBuildingsActiveHours()
    {
        return Building::initializeBuildingsActiveHours();
    }

    public function buildingsIndex()
    {
        $this->crud->addClause('with', 'flats');

        return view('index.buildings');
    }


    protected function setupListOperation()
    {
        $this->crud->removeButton('show');

        $fields = new BuildingFields();

        $this->crud->addFields($fields->getFormFields(), 'both');
        $this->crud->addColumns($fields->getColumnsFields());

        $this->crud->addClause('where', 'lead', '=', null); //Es importante que se mantenga como primer clausula (Porque en scopes se hace unset wheres[0] de la query)

        $this->crud->addFilter([ // simple filter
            'type' => 'simple',
            'name' => 'trashed',
            'label' => 'Mostrar eliminados'
        ],
            false,
            function () { // if the filter is active
                $this->crud->addClause('onlyDeleted');
                $this->crud->addButtonFromView('line', 'restoreBuilder', 'restoreBuilder', 'beginning');
                $this->crud->denyAccess(['create', 'update', 'reorder', 'delete', 'revise']);
            });

        if (backpack_user()->hasRole(['Admin', 'Jefe Atención al Cliente', 'Representante Atención al Cliente', 'Jefe Comercial', 'Auxiliar Comercial'])) {
            $this->crud->addFilter([ // simple filter
                'type' => 'simple',
                'name' => 'lead',
                'label' => '/ Mostrar prospectos'
            ],
                false,
                function () { // if the filter is active
                    $this->crud->addClause('isLead');
                    $this->crud->addButtonFromView('line', 'restoreBuilder', 'restoreBuilder', 'beginning');
                    $this->crud->denyAccess(['create', 'reorder', 'revise']);
                    $this->crud->addButtonFromView('line', 'leadInfo', 'change_to_building');
                    if (backpack_user()->hasRole(['Representante Atención al Cliente', 'Jefe Atención al Cliente'])) {
                        $this->crud->denyAccess(['create', 'reorder', 'revise', 'delete']);
                    }
                });
        }

        $this->crud->addFilter([
            'name' => 'cases',
            'type' => 'simple',
            'label' => '/ Casos'
        ], false,
            function () { // if the filter is active
                $this->crud->addClause('numberCases');

            });

        $this->crud->addFilter([
            'name' => 'lanzamientos',
            'type' => 'simple',
            'label' => '/ Lanzamientos'
        ], false,
            function () { // if the filter is active
                $this->crud->addClause('where', 'service_start_date', '>', Carbon::now());

            });

    }

    protected function setupCreateOperation()
    {
        if (backpack_user()->hasRole('Admin')) {
            $this->crud->setValidation(BuildingRequest::class);
            $fields = new BuildingFields();
            $this->crud->addFields($fields->getFormFields(), 'both');
            $this->crud->addColumns($fields->getColumnsFields());
        } elseif (backpack_user()->hasRole(['Representante Atención al Cliente', 'Jefe Atención al Cliente'])) {
            $this->crud->setValidation(BuildingRequest::class);
            $fields = new BuildingFields();
            $this->crud->addFields($fields->getFormFieldsSpecial(), 'both');
            $this->crud->addColumns($fields->getColumnsFields());
        } elseif (backpack_user()->hasRole(['Jefe Comercial', 'Auxiliar Comercial'])) {
            $this->crud->setValidation(BuildingRequest::class);
            $fields = new BuildingComercialFields();
            $this->crud->addFields($fields->getFormFields(), 'both');
            $this->crud->addColumns($fields->getColumnsFields());
        } elseif (backpack_user()->hasRole(['Jefe Técnico'])) {
            $this->crud->setValidation(BuildingRequest::class);
            $fields = new BuildingClientFields();
            $this->crud->addFields($fields->getFormFields(), 'both');
            $this->crud->addColumns($fields->getColumnsFields());
        } elseif (backpack_user()->hasRole(['Auxiliar Ingeniería', 'Jefe Ingeniería'])) {
            $this->crud->setValidation(BuildingEngineeringRequest::class);
            $fields = new BuildingEngineeringFields();
            $this->crud->addFields($fields->getFormFields(), 'both');
            $this->crud->addColumns($fields->getColumnsFields());
        }
    }

    protected function setupUpdateOperation()
    {
        if (backpack_user()->hasRole('Jefe Técnico')) {
            $fields = new BuildingFields();
            $this->crud->addFields($fields->getFormFieldsTechnic(), 'both');
        } else {
            $this->setupCreateOperation();
        }

    }


    public function buildingContactsInfo($id)
    {
        return (string)Building::findOrFail($id)->contacts()->with('flat')->with('building')->get();
    }

    public function buildingSortedContacts($id)
    {
        $building = Building::findOrFail($id);
        $contacts = $building->verificateContacts()->with('flat')->with('building')->with('casesOpen')->get();
        $contacts_two = $building->contactsSecondary()->with('flat')->with('building')->with('casesOpen')->with('secondaryInfo')->get();
        return $building->sortOfContactType($contacts->merge($contacts_two));
    }

    public function buildingSortedContactsMinimized($id)
    {
        return $this->contactsMinimizedInDashboard($id);
    }

    public function contactsMinimizedInDashboard($id)
    {
        $building = Building::with(['contacts' => function ($q) {
            $q->with(['contact' => function ($q2) {
                $q2->withOut('mainBuilding');
            }]);
        }])->where('id', $id)->first();

        $contacts = $building->verificateContactsMultiple();

        $contactsCollection = BuildingContactResource::collection($contacts);

        return $building::sortedContacts($contactsCollection);
    }


    public function buildingSortedContactsVerificate($id)
    {
        $building = Building::findOrFail($id);
        $contactsAuthorized = $building->verificateContactsMultiple(true, true);

        $contacts = BuildingContactResource::collection($contactsAuthorized);

        return Building::sortedContacts($contacts);
    }

    public function buildingSortedContactsVerificateCadete($id)
    {
        $contacts = Contact::whereHas('contacts', function (Builder $query) use ($id) {
            $query->where('building_id', $id);
        })->with(['flat', 'building', 'cases'])
            ->where('needs_verification', 2)->get();

        $building = Building::findOrFail($id);

        return ContactInBuildingResource::collection($building::sortedContacts($contacts));
    }

    public function buildingSortedContactsComision($id)
    {
        $building = Building::findOrFail($id);
        $contactsAuthorized = $building->verificateContactsMultiple()
            ->filter(function ($contact) {
                return str_contains($contact->contact_type, 'Comisión');
            });

        $contacts = BuildingContactResource::collection($contactsAuthorized);

        return Building::sortedContacts($contacts);
    }


    public function buildingSortedContactsAuthorizations($id)
    {
        $building = Building::findOrFail($id);
        $contactsAuthorized = $building->verificateContactsMultiple(false)
            ->filter(function ($contact) {
                return is_null($contact->start_date) && is_null($contact->end_date);
            });

        $contactCollection = BuildingContactResource::collection($contactsAuthorized);

        return $building::sortedContacts($contactCollection);
    }

    public function buildingSortedContactsAuthorizationsTemporary($id)
    {
        $building = Building::findOrFail($id);
        $contactsAuthorized = $building->verificateContactsMultiple(false)
            ->filter(function ($contact) {
                return !is_null($contact->start_date) && !is_null($contact->end_date);
            });

        $contactCollection = BuildingContactResource::collection($contactsAuthorized);

        return $building::sortedContacts($contactCollection);
    }

    public function buildingSortedContactsReferrer($id)
    {
        $building = Building::findOrFail($id);
        $contactsAuthorized = $building->verificateContactsMultiple()
            ->filter(function ($contact) {
                return $contact->referer;
            });

        $contacts = BuildingContactResource::collection($contactsAuthorized);

        return Building::sortedContacts($contacts);
    }

    public function buildingTemporaryContactsInfo($id)
    {
        $temporary = Building::findOrFail($id)->temporaryContacts()->with('building')->with('flat')->get();
        return ActiveTemporaryPermitsResource::collection($temporary);
    }

    public function buildingServicesInfo($id)
    {
        return json_encode(ServiceInDashboardResource::collection(Building::findOrFail($id)->sortedServices()));
    }

    public function buildingServicesInfoMinimized($id)
    {
        $building = Building::findOrFail($id);
        return ServiceResource::collection($building->services);
    }


    public function showFlats($building_id)
    {
        $building = Building::findOrFail($building_id);

        return response()->json($building->flats()->get());
    }

    public function viewLogs($id)
    {
        $building_number = Building::findOrfail($id)->building_number;

        return redirect('/admin/building-log?buildingId=' . $building_number);
    }

    public function sendNotifyForODT($building, $old_building_order_job, $old_building_order_job_checked)
    {
        if ($building->order_job != null && $building->order_job != $old_building_order_job) {
            $areas = Area::where('name', 'Mantenimiento')->orWhere('name', 'Ingeniería')->pluck('id');
            $roles = Roles::whereIn('area_id', $areas)->get();
            $old_reponsables = [];
            foreach ($roles as $role) {
                $responsables = $role->responsables();
                foreach ($responsables as $responsable) {
                    if (!in_array($responsable->id, $old_reponsables)) {
                        $responsable->notify(new ODTNotification($building));
                    }
                    array_push($old_reponsables, $responsable->id);
                }
            }
        }

        if ($building->order_job_checked != null && $building->order_job_checked != $old_building_order_job_checked) {
            $roles = Roles::find(Config('constants.logistics_responsible_id'));
            $old_reponsables = [];
            $responsables = $roles->responsables();
            foreach ($responsables as $responsable) {
                if (!in_array($responsable->id, $old_reponsables)) {
                    $responsable->notify(new ODTNotification($building));
                }
                array_push($old_reponsables, $responsable->id);
            }
        }
    }

    public function getInfoOfTheRelationBuilding($building_id, $flat_id, $contact_id)
    {
        $new_contact_tipe = BuildingContact::where('building_id', $building_id)->where('flat_id', $flat_id)->where('contact_id', $contact_id)->first()->contact_type;
        $principal_type = Contact::find($contact_id)->getIconOfType($new_contact_tipe);
        $second_type = Contact::find($contact_id)->getOthersTypesOfContact($new_contact_tipe);
        return json_encode([$principal_type, $second_type]);
    }

    public function getAllBuildings(Request $request)
    {

        if (array_key_exists('q', $request->all())) {
            $buildings = Building::where('name', 'like', '%' . $request->all()['q'] . '%')
                ->orwhere('building_number', 'like', '%' . $request->all()['q'] . '%')->get();
        } else {
            $buildings = Building::all();
        }

        return response(['message' => 'Lista de edificios', 'data' => $buildings], 200);
    }

    public function hasAccessByPin($userId, $flatId)
    {
        $phone_mobile = BuildingContact::query()->where('contact_id', $userId)->value('phone_mobile');
        $building = Flat::find($flatId)?->building;
        $accessByPin = $building?->access_by_pin;
        $permanentCode = $building?->accessCodes()
            ->where('flat_id', $flatId)
            ->where('user_id', $userId)
            ->whereNull('expires_at')
            ->first();
        $temporaryCode = $building?->accessCodes()
            ->where('flat_id', $flatId)
            ->where('user_id', $userId)
            ->whereNotNull('expires_at')
            ->get();
        return response()->json([
            'access_by_pin' => $accessByPin,
            'code' => $permanentCode,
            'access_code_temporal_show' => $temporaryCode,
            'phone_mobile' => $phone_mobile && trim($phone_mobile) != ''
        ]);
    }

    public function buildingHasAccessByPin($id)
    {
        $accessByPin = (bool)Building::query()->where('id', $id)->first()?->access_by_pin != 0;
        return response()->json([
            'access_by_pin' => $accessByPin,
        ]);
    }

    /**
     * @throws \Exception
     */
    public function deleteAccessCodeForUserFromFlat($userId, $flatId)
    {
        if ($userId & $flatId) {
            \DB::beginTransaction();
            try {
                $codes = AccessCode::query()
                    ->where('flat_id', $flatId)
                    ->where('user_id', $userId)->get();
                $buildingId = Flat::query()->find($flatId)?->building_id;
                foreach ($codes as $code) {
                    $code->deleteForAkuvoxOr2NAndDatabase($userId, $code, $buildingId, true);
                }
                \DB::commit();
            } catch (\Throwable $e) {
                \DB::rollBack();
                throw new \Exception($e->getMessage());
            }
        }
    }

    public function getSchedule($user_id, $flat_id)
    {
        return Contact::getFakeSchedule($user_id, $flat_id);
    }

    public function getBuildings(Request $request)
    {
        $search = $request->input('search');
        $buildings = Building::query()
            ->when($search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%")->orwhere('building_number', 'like', "%$search%");
            })
            ->get(['id', 'name', 'building_number']);
        return response()->json($buildings);
    }

    public function getBuildingsInfo(Request $request)
    {
        $search = $request->input('search');
        $buildings = Building::query();

        if ($search !== null) {
            if ($search === '0') {
                $buildings->where(function ($query) {
                    $query->where('building_number', '0')
                        ->orWhere('name', 'like', '%0%');
                });
            } else {
                $buildings->where(function ($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%")
                        ->orWhere('building_number', 'like', "%{$search}%");
                });
            }
        }

        $buildings = $buildings->get(['id', 'name', 'building_number']);
        return response()->json($buildings);
    }


}
